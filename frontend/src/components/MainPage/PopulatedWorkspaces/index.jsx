import React, { useMemo } from "react";
import { Lu<PERSON>erve<PERSON>, Lu<PERSON>loudUpload } from "react-icons/lu";
import { useTranslation } from "react-i18next";
import { useTabNames } from "@/stores/settingsStore";
import { getWorkspaceTranslatedName } from "@/utils/workspace";
import paths from "@/utils/paths";
import useWorkspaceStore from "@/stores/workspaceStore";
import { useSetSelectedModule } from "@/stores/userStore";
import { getLocalizedDate } from "@/utils/dates";

export default function PopulatedWorkspaces() {
  const workspaces = useWorkspaceStore((state) => state.populatedWorkspaces);
  const loading = useWorkspaceStore((state) => state.loadingPopulated);
  const { t } = useTranslation();
  const tabNames = useTabNames();
  const setSelectedModule = useSetSelectedModule();

  const sortedWorkspaces = useMemo(() => {
    if (!workspaces?.length) return [];
    return [...workspaces].sort((a, b) => {
      const dateA = new Date(a.lastUpdatedAt || a.createdAt);
      const dateB = new Date(b.lastUpdatedAt || b.createdAt);
      return dateB - dateA;
    });
  }, [workspaces]);

  const getAiType = (type) => {
    switch (type) {
      case "legal-qa":
        if (tabNames?.tabName1 && tabNames.tabName1.trim() !== "") {
          return tabNames.tabName1;
        }
        return t("module.legal-qa");

      case "document-drafting":
        if (tabNames?.tabName2 && tabNames.tabName2.trim() !== "") {
          return tabNames.tabName2;
        }
        return t("module.document-drafting");

      case "active-case":
        if (tabNames?.tabName3 && tabNames.tabName3.trim() !== "") {
          return tabNames.tabName3;
        }
        return t("module.active-case");

      default:
        console.warn(`Unknown workspace type: ${type}`);
        return type === "document-drafting"
          ? t("workspace.local-ai")
          : t("workspace.cloud-ai");
    }
  };

  const workspaceIcons = {
    "document-drafting": <LuServer className="size-5 text-muted" />,
    "legal-qa": <LuCloudUpload className="size-5 text-muted" />,
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    const today = new Date();
    if (date.toDateString() === today.toDateString()) {
      return t("workspace.today-time", {
        time: `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`,
      });
    }

    const localizedDateString = getLocalizedDate(date, t);
    const time = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    return `${localizedDateString}, ${time}`;
  };

  return (
    <div className="mt-14 max-w-5xl">
      <h2 className="text-2xl font-medium mb-2 md:mb-4 text-foreground">
        {t("workspace.my-workspaces")}
      </h2>
      {loading ? (
        <div className="w-full animate-pulse border border-border rounded-xl overflow-hidden">
          <div className="h-10 w-full border-b border-border flex items-center px-6">
            <div className="h-3 w-2/5 bg-border rounded"></div>
            <div className="h-3 w-1/6 bg-border rounded ml-8"></div>
            <div className="h-3 w-16 bg-border rounded ml-auto"></div>
          </div>
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="h-16 w-full bg-elevated border-t border-border flex items-center px-6"
            >
              <div className="h-4 w-2/5 bg-border rounded mr-8"></div>
              <div className="h-4 flex items-center gap-2 w-1/4">
                <div className="h-5 w-5 rounded-full bg-border"></div>
                <div className="h-4 w-20 bg-border rounded"></div>
              </div>
              <div className="h-4 w-16 bg-border rounded ml-auto"></div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {!workspaces?.length ? (
            <p className="text-lg">{t("workspace.no-workspaces")}</p>
          ) : (
            <div className="w-full overflow-x-auto rounded-xl border border-border">
              <table className="w-full">
                <thead className="border-b border-border text-foreground">
                  <tr>
                    <th className="px-6 py-3 text-left font-medium tracking-wider w-2/5">
                      {t("workspace.name")}
                    </th>
                    <th className="px-6 py-3 text-left font-medium tracking-wider">
                      {t("workspace.ai-type")}
                    </th>
                    <th className="px-6 py-3 text-right font-medium tracking-wider">
                      {t("workspace.latest-activity")}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-elevated divide-y divide-border">
                  {sortedWorkspaces.map((workspace) => (
                    <tr
                      key={workspace.id}
                      className="hover:bg-secondary transition-colors cursor-pointer"
                      onClick={() => {
                        setSelectedModule(workspace.type);
                        window.location.href = paths.workspace.chat(
                          workspace.slug
                        );
                      }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap w-2/5">
                        <p className="font-medium text-foreground">
                          {getWorkspaceTranslatedName(workspace.name, t)}
                        </p>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2 text-muted">
                          {workspaceIcons[workspace.type]}
                          <p>{getAiType(workspace.type)}</p>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-muted">
                        {formatDate(
                          workspace.lastUpdatedAt || workspace.createdAt
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
    </div>
  );
}
